module.exports = {
  content: ['./src/**/*.{html,js,svelte,ts}'],
  theme: {
    extend: {},
  },
  plugins: [
    // https://github.com/tailwindlabs/tailwindcss-typography
    // 用于美化纯文本内容的排版 只要在文章顶层标签的class中加 prose 类名
    '@tailwindcss/typography', 
    'daisyui', // 添加 Daisy UI
  ],
  // DaisyUI 配置 (可选，但推荐)
  daisyui: {
    themes: [
      // 默认亮色主题
      "light",
      {
        "light": {
          "neutral": "hsl(var(--primary))"
        }
      },
      // "dark",  // 暗色主题
      // ... 更多主题
    ],
    // 如果你希望 'dark' 主题在用户系统偏好设置为暗色时自动激活
    // darkTheme: "dark",
    // base: true,   // 应用 DaisyUI 的基本样式，推荐保持 true
    // styled: true, // 应用 DaisyUI 组件的样式，推荐保持 true
    // utils: true,  // 应用 DaisyUI 的辅助工具类，推荐保持 true
    // logs: true,   // 在控制台打印 DaisyUI 日志
    // rtl: false,   // 右到左布局
    // prefix: "",   // CSS 类前缀 (不常用，除非有命名冲突)
  },
};