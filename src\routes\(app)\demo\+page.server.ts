import type { PageServerLoad } from './$types'; // 导入 SvelteKit 自动生成的类型


interface DemoData {
  message: string;
  timestamp: string;
  value: number;
}

// 定义一个异步函数来模拟获取数据
// 注意：这里我们不指定 getData 的返回类型，让 TypeScript 自动推断
async function getData(): Promise<DemoData> {
  const data = {
    message: '这是一个自动推断的类型',
    timestamp: new Date().toISOString(),
    value: 123
  };

  return new Promise(resolve => {
    setTimeout(() => {
      resolve(data);
    }, 500);
  });
}

// 导出 load 函数
// SvelteKit 会分析这个 load 函数的返回值。
// : PageServerLoad 注解会帮助 TypeScript 确保函数签名符合 SvelteKit 要求。
export const load: PageServerLoad = async () => {
  const data = await getData();
  
  // load 函数直接返回数据对象。
  // SvelteKit 会根据这个对象的结构自动生成 PageData 类型。
  return {
    simpleData: data, // 你返回的数据会被包装在这个属性下
    anotherField: '这是一个额外字段'
  };
};