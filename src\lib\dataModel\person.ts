// 性别枚举
export enum Gender {
  Male = 1,
  Female = 2
}

export const GenderMap: { [key in Gender]: string } = {
  [Gender.Male]: '男',
  [Gender.Female]: '女'
};

// 当前身份枚举
export enum CurrentIdentity {
  Student = 1,
  Graduated = 2
}

export const CurrentIdentityMap: { [key in CurrentIdentity]: string } = {
  [CurrentIdentity.Student]: '在校学生',
  [CurrentIdentity.Graduated]: '毕业工作'
};

// 学习状态枚举
export enum LearningStatus {
  DaYi = 1,
  DaEr = 2,
  DaSan = 3,
  DaSi = 4,
  YanYi = 5,
  YanEr = 6,
  Yan<PERSON>an = 7,
  BoYi = 8,
  BoEr = 9,
  BoSan = 10,
  BoSi = 11,
}

export const LearningStatusMap: { [key in LearningStatus]: string } = {
  [LearningStatus.DaYi]: '大一',
  [LearningStatus.DaEr]: '大二',
  [LearningStatus.DaSan]: '大三',
  [LearningStatus.DaSi]: '大四',
  [LearningStatus.YanYi]: '研一',
  [LearningStatus.YanEr]: '研二',
  [LearningStatus.YanSan]: '研三',
  [LearningStatus.BoYi]: '博一',
  [LearningStatus.BoEr]: '博二',
  [LearningStatus.BoSan]: '博三',
  [LearningStatus.BoSi]: '博四',
};


// 工作状态枚举
export enum WorkingStatus {
  OneToThree = 1,
  ThreeToFive = 2,
  FiveToTen = 3,
  MoreThanTen = 4
}

export const WorkingStatusMap: { [key in WorkingStatus]: string } = {
  [WorkingStatus.OneToThree]: '工作1-3年',
  [WorkingStatus.ThreeToFive]: '工作3-5年',
  [WorkingStatus.FiveToTen]: '工作5-10年',
  [WorkingStatus.MoreThanTen]: '工作10年以上',
};
