<script lang="ts">
	// 导入必要的 SvelteKit 模块，如果需要客户端导航
	import { goto } from '$app/navigation';

	// 用于绑定输入字段的值
	let email = '';
	let password = '';
	let rememberMe = false;
	let loading = false; // 用于控制加载状态
	let errorMessage: string | null = null; // 用于显示错误信息

	// 模拟登录逻辑
	async function handleLogin() {
		errorMessage = null; // 清除之前的错误信息
		loading = true; // 设置加载状态

		// 实际项目中，你会在这里调用你的后端 API
		// 例如：await post('/api/login', { email, password });
		await new Promise((resolve) => setTimeout(resolve, 1500)); // 模拟网络请求

		if (email === '<EMAIL>' && password === 'password') {
			// 登录成功
			console.log('登录成功！', { email, password, rememberMe });
			loading = false;
			await goto('/'); // 登录成功后跳转到首页
		} else {
			// 登录失败
			errorMessage = '邮箱或密码不正确。请重试。';
			loading = false;
		}
	}

	// 可以在这里添加一些验证逻辑
	$: isFormValid = email.length > 0 && password.length > 0;
</script>

<div class="bg-base-200 flex min-h-screen items-center justify-center p-4">
	<div class="card bg-base-100 w-full max-w-sm shadow-2xl">
		<div class="card-body">
			<h2 class="card-title text-primary mb-6 text-center text-3xl font-bold">欢迎登录</h2>
			<p class="text-base-content/70 mb-8 text-center text-sm">输入您的凭据以访问您的账户</p>

			<form on:submit|preventDefault={handleLogin} class="space-y-4">
				<div class="form-control">
					<label class="label" for="email">
						<span class="label-text">邮箱</span>
					</label>
					<input
						type="email"
						id="email"
						placeholder="您的邮箱地址"
						class="input input-bordered w-full {errorMessage ? 'input-error' : ''}"
						bind:value={email}
						required
					/>
				</div>

				<div class="form-control">
					<label class="label" for="password">
						<span class="label-text">密码</span>
					</label>
					<input
						type="password"
						id="password"
						placeholder="您的密码"
						class="input input-bordered w-full {errorMessage ? 'input-error' : ''}"
						bind:value={password}
						required
					/>
					<label class="label">
						<a href="/forgot-password" class="label-text-alt link link-hover">忘记密码？</a>
					</label>
				</div>

				<div class="form-control flex-row items-center justify-between">
					<label class="label cursor-pointer py-0">
						<input type="checkbox" class="checkbox checkbox-primary mr-2" bind:checked={rememberMe} />
						<span class="label-text">记住我</span>
					</label>
				</div>

				{#if errorMessage}
					<div role="alert" class="alert alert-error px-3 py-2 text-sm">
						<svg
							xmlns="http://www.w3.org/2000/svg"
							class="h-6 w-6 shrink-0 stroke-current"
							fill="none"
							viewBox="0 0 24 24"
						>
							<path
								stroke-linecap="round"
								stroke-linejoin="round"
								stroke-width="2"
								d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"
							/>
						</svg>
						<span>{errorMessage}</span>
					</div>
				{/if}

				<div class="form-control mt-6">
					<button type="submit" class="btn btn-primary w-full" disabled={loading || !isFormValid}>
						{#if loading}
							<span class="loading loading-spinner"></span>
							登录中...
						{:else}
							登录
						{/if}
					</button>
				</div>
			</form>

			<div class="divider">或</div>
			<p class="text-base-content/80 text-center text-sm">
				还没有账户？<a class="link link-primary font-semibold">立即注册</a>
			</p>
		</div>
	</div>
</div>

<style lang="postcss">
	/* 这里可以放置额外的自定义 CSS，但 DaisyUI 和 Tailwind 已经做得很好 */
</style>
