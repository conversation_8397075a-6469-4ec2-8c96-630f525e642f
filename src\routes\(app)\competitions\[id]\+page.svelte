<script>
	import { page } from '$app/state';
	import Detail from './detail.svelte';
	import SignUp from './signUp.svelte';

	const { id } = page.params;

	const data = {
		id,
		name: '比赛名称比赛名称比赛名称',
		description:
			'比赛描述比赛描述比赛描述比赛描述比赛描述比赛描述比赛描述比赛描述比赛描述比赛描述比赛描述比赛描述比赛描述比赛描述比赛描述比赛描述比赛描述比赛描述比赛描述比赛描述比赛描述比赛描述比赛描述比赛描述比赛描述比赛描述比赛描述比赛描述比赛描述比赛描述比赛描述比赛描述比赛描述',
		image:
			'https://bj.bcebos.com/v1/ai-studio-match/file/48d721bdd6db434c839652d51b941d55efaa47d838a44c3db7e428c54852b6f3?authorization=bce-auth-v1%2F5cfe9a5e1454405eb2a975c43eace6ec%2F2025-05-09T05%3A44%3A43Z%2F-1%2F%2F16ea85cc0083e2b47dd91f789895c887b549b24e81208704e242f1ed301c68f5',
		startTime: '2025-01-01 00:00:00',
		endTime: '2025-01-05 23:59:59',
		sponsor: 'xxxx',
		// 报名人数
		applicantCount: 999
	};

	// 是否为详情页   false为报名页
	let isDesPage = true;

	function handleSignUp() {
		isDesPage = !isDesPage;
	}
</script>

<!-- 顶部信息 -->
<div class="flex flex-col items-center border-b border-solid border-gray-200 px-2 pt-5 pb-10 lg:flex-row lg:px-10">
	<img src={data.image} alt="competition-data" class="mr-10 hidden h-45 w-45 lg:block" />
	<div class="flex-1 lg:mr-20 lg:ml-5">
		<div class="mb-3 text-2xl">{data.name}</div>
		<div class="tooltip tooltip-bottom" data-tip={data.description}>
			<div class="mb-3 lg:line-clamp-3" title="">{data.description}</div>
		</div>
		<div class="mb-3">比赛时间：{data.startTime} - {data.endTime}</div>
		<div>主办方：{data.sponsor}</div>
	</div>

	<div class="mt-10 flex w-full flex-col lg:mt-0 lg:w-auto">
		<button class="btn btn-primary btn-lg w-full lg:w-auto mb-5" onclick={handleSignUp}>立即报名</button>
		<div class="text-sm text-gray-500">报名人数：{data.applicantCount}</div>
	</div>
</div>

{#if isDesPage}
	<!-- 报名表单页 -->
	<SignUp />
{:else}
	<!-- 比赛详情页 -->
	<Detail />
{/if}
