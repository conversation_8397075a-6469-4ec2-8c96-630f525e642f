<script lang="ts">
	import { validatorItemHandle, type FormElType, type FormElValue } from './index';

	let {
		itemObj,
		value = $bindable(),
		formData,
		disabled,
		hideError
	}: {
		itemObj: FormElType;
		value: FormElValue;
		formData?: { [key: string | number]: any }; // 表单对象，主要拿来给validator传参
		disabled?: boolean;
		hideError?: boolean; // 隐藏验证失败时的提示
	} = $props();

	let error = $state<boolean | string>(false);

	// 输入框失去焦点时触发
	export function handleBlur(): boolean {
		const res = validatorItemHandle(itemObj, value, formData);
		if (res) {
			error = res;
			return false;
		}

		error = false;
		return true;
	}

  // 字符串去除前后空格
  export function stringTrim() {
    if(['text', 'textarea', 'email', 'phone'].includes(itemObj.type ?? 'text') && value && typeof value === 'string') value = value.trim();
  }

  // 城市选择
  let choseCity = $state(['', '', '']);

  let cityData: { [key: string]: {[key: string]: string} } = $state.raw({});
  if(itemObj.type === 'city') {
    import('./city.json').then((res) => {
      cityData = res.default;
    });
  }

  $effect(() => {
    if(itemObj.type === 'city') value = JSON.stringify(choseCity);
	});
  
</script>

<div>
	<div>
		{#snippet inputEl(type: 'text' | 'number' | 'password')}
			<input
				{type}
				class={[
					'input input-sm',
					{
						'input-error': error
					}
				]}
        name={itemObj.key}
				style:width={itemObj.width ? itemObj.width : '100%'}
				bind:value
				required={itemObj.required ?? false}
				disabled={itemObj.disabled || disabled}
				onblur={handleBlur}
				oninput={stringTrim}
			/>
		{/snippet}

		{#if !itemObj.type || ['text', 'email', 'phone'].includes(itemObj.type)}
			{@render inputEl('text')}
		{:else if itemObj.type === 'number'}
			{@render inputEl('number')}
		{:else if itemObj.type === 'password'}
			{@render inputEl('password')}
    <!-- 如果是多行文本 -->
    {:else if itemObj.type === 'textarea'}
			<textarea
				class={[
					'textarea textarea-sm',
					{
						'textarea-error': error
					}
				]}
        name={itemObj.key}
				style:width={itemObj.width ? itemObj.width : '100%'}
				bind:value
				required={itemObj.required ?? false}
				disabled={itemObj.disabled || disabled}
				onblur={handleBlur}
				oninput={stringTrim}
			></textarea>
			<!-- 如果是单选 -->
		{:else if itemObj.type === 'radio'}
			<div class="flex gap-7 flex-wrap pt-1">
				{#each itemObj.options ?? [] as option}
					<label class="flex cursor-pointer items-center gap-1">
						<input
							type="radio"
							name={itemObj.key}
              class={[
                'radio radio-sm',
                {
                  'radio-primary': !error,
                  'radio-error': error
                }
              ]}
							value={option.value}
							bind:group={value}
							disabled={itemObj.disabled || disabled}
						/>
						<span>{option.label}</span>
					</label>
				{/each}
			</div>
      <!-- 如果是多选 -->
    {:else if itemObj.type === 'checkbox'}
			<div class="flex gap-7 flex-wrap pt-1">
				{#each itemObj.options ?? [] as option}
					<label class="flex cursor-pointer items-center gap-1">
						<input
							type="checkbox"
							name={itemObj.key}
              class={[
                'checkbox checkbox-sm',
                {
                  'checkbox-primary': !error,
                  'checkbox-error': error
                }
              ]}
							value={option.value}
							bind:group={value}
							disabled={itemObj.disabled || disabled}
						/>
						<span>{option.label}</span>
					</label>
				{/each}
			</div>
			<!-- 如果是开关 -->
		{:else if itemObj.type === 'switch'}
			<div class="flex gap-7 pt-1">
				<label class="flex cursor-pointer items-center gap-1">
					<input
						type="checkbox"
            name={itemObj.key}
						class="toggle toggle-primary toggle-sm"
						bind:checked={value as any}
						disabled={itemObj.disabled || disabled}
					/>
					<span>{itemObj.label}</span>
				</label>
			</div>
			<!-- 如果是下拉框 -->
		{:else if itemObj.type === 'select'}
			<div class="relative group">
				<select
          class={[
            'select select-sm',
            {
              'select-error': error
            }
          ]}
          style:width={itemObj.width ? itemObj.width : '100%'}
					bind:value
          name={itemObj.key}
					required={itemObj.required ?? false}
					disabled={itemObj.disabled || disabled}
					onblur={handleBlur}
				>
					{#each itemObj.options ?? [] as option}
						<option value={option.value}>{option.label}</option>
					{/each}
				</select>
				<!-- 清空按钮 -->
				<!-- {#if value !== undefined && value !== null && value !== ''}
					<button
						type="button"
						class="absolute top-1/2 right-8 -translate-y-1/2 text-gray-500 hover:text-gray-700 opacity-0 group-hover:opacity-100 transition-opacity"
						aria-label="Clear selection"
						onclick={() => {
							value = null;
							handleBlur();
						}}
					>
						<svg
							xmlns="http://www.w3.org/2000/svg"
							class="h-4 w-4"
							fill="none"
							viewBox="0 0 24 24"
							stroke="currentColor"
						>
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
						</svg>
					</button>
				{/if} -->
			</div>

      <!-- 如果是城市 -->
    {:else if itemObj.type === 'city'}
      {#snippet citySelect(index: number)}
        <select
          class={[
            'select select-sm w-36',
            {
              'select-error': error
            }
          ]}
					bind:value={choseCity[index]}
					required={itemObj.required ?? false}
					disabled={itemObj.disabled || disabled}
					onblur={handleBlur}
          onchange={() => {
            if(index === 0) {
              choseCity[1] = '';
              choseCity[2] = '';
            } else if (index === 1) {
              choseCity[2] = '';
            }
          }}
				>
					{#each Object.entries(cityData[index === 0? "86": choseCity[index - 1]] ?? {}) as option}
						<option value={option[0]}>{option[1]}</option>
					{/each}
				</select>
      {/snippet}

      <!-- 隐藏的输入框，用来提交数据 -->
      <input class="hidden" type="text" name={itemObj.key} {value} >
        <!-- flex超出运行换行 -->
			<div class="flex gap-2 flex-wrap">
        <!-- 一级城市 -->
        {@render citySelect(0)}
        <!-- 二级城市 -->
				{#if choseCity[0] &&  [2, 3, null, undefined].includes(itemObj.cityLevel)}
          {@render citySelect(1)}
        {/if}
        <!-- 三级城市 -->
				{#if choseCity[1] && [3, null, undefined].includes(itemObj.cityLevel)}
          {@render citySelect(2)}
        {/if}
			</div>
		{/if}
	</div>

	{#if !hideError}
		<p class="h-3.5 text-xs leading-3.5 text-[var(--color-error)]">
			{#if error}
				{error}
			{/if}
		</p>
	{/if}
</div>
