/*
 * @Author: Strayer
 * @Date: 2025-06-23
 * @LastEditors: Strayer
 * @LastEditTime: 2025-06-24
 * @Description: 
 * @FilePath: \training-camp-web\src\lib\components\form\index.ts
 */
// 表单选项公共组件数据格式

import { isNumber, isString } from "remeda"
import validator from "validator"

type ComponentType =
  'text'
  | 'textarea'
  | 'number'
  | 'radio'
  | 'checkbox'
  | 'switch'
  | 'select'
  | 'password'
  | 'email'
  | 'phone'
  | 'city'

export interface FormElType {
  key: string,
  label: string,
  type?: ComponentType, //所使用的组件 text
  required?: boolean, // 是否必填
  disabled?: true, // 是否禁止编辑
  hidden?: boolean, // 是否隐藏
  width?: string,
  validator?: { // 默认是失去焦点时触发
    // number
    min?: number,
    max?: number,

    custom?: (value: any, formData: any) => string | null | undefined, 
  }
  // formEl部分可使用代码片段
  useFormElSnippet?: boolean,

  // ---------select | radio | checkbox -----
  options?: Array<{
    value: number | string,
    label: string,
  }>

  // 城市嵌套级别 2 | 3. 默认是3级
  cityLevel?: 2 | 3,
}

export type FormElValue = undefined | null | string | number | boolean;

/**
 * @description: 单条数据验证
 */
export function validatorItemHandle(itemObj: FormElType, value: FormElValue, formData?: any) {
  // 必填
		if (itemObj.required && !value && value !== false) {
			return itemObj.label + '不能为空';
		}

		// 最小范围
		if (itemObj.type === 'number' && isNumber(itemObj.validator?.min) && isNumber(value)) {
			if (value < itemObj.validator.min) {
				return '最小值为' + itemObj.validator.min;
			}
		}
		// 最大范围
		if (itemObj.type === 'number' && isNumber(itemObj.validator?.max) && isNumber(value)) {
			if (value > itemObj.validator.max) {
				return '最大值为' + itemObj.validator.max;
			}
		}

		// 是否符合邮箱格式
		if (itemObj.type === 'email' && value && isString(value) && !validator.isEmail(value)) {
			return '请输入正确的邮箱格式';
		}

		// 是否符合电话号码格式
		if (itemObj.type === 'phone' && value && isString(value) && !validator.isMobilePhone(value.toString(), 'zh-CN')) {
			return '请输入正确的电话号码格式';
		}

    // 城市选择
    if (itemObj.type === 'city' && value && isString(value)) {
      const cityValue = JSON.parse(value);
      const cityLevel = itemObj.cityLevel ?? 3;

      if(!cityValue[0]) {
        return '请选择一级城市'
      }
      if([2, 3].includes(cityLevel) && !cityValue[1]) {
        return '请选择二级城市'
      }
      if(cityLevel === 3 && !cityValue[2]) {
        return '请选择三级城市'
      }
    }

		// 自定义验证
		return itemObj.validator?.custom?.(value, formData);
}
