<script lang="ts">
	import { validatorItemHandle, type FormElType, type FormElValue } from './index';
	import FormEl from './formEl.svelte';
	import type { Snippet } from 'svelte';
	let {
		options,
		value = $bindable(),
		disabled,
    formEl,
    hideSubmitButton,
    url,
    footerEl
	}: {
		options: FormElType[]; // 表单选项
		value: { [key: string | number]: any }; // 表单对象
		disabled?: boolean;
    formEl?: Snippet<[FormElType]>, // 表单项自定义片段
    hideSubmitButton?: boolean; // 隐藏提交按钮
    url?: string; // 表单提交地址 默认是："?/submit"
    footerEl?: Snippet, // 底部自定义片段, 是嵌入form表单内的,所以能触发表单的事件
	} = $props();

  // FormEl 实例
  let formElInstance: { [key: string]: any } = {};

  /**
   * @description: 对整个表单进行验证, 返回false说明验证不通过
   * 用export导出支持外部调用
   */   
  export function validator() {
    console.log('%c [ formData: ]-8', 'font-size:13px; background:#0f7668; color:#53baac;', value);

    let res = true;
    for(const item of Object.values(formElInstance)) {
      if (!item.handleBlur()) res = false;
    }

    return res;
  }

  function onclickHandle(e: Event) {
    if (!validator()) {
      e.preventDefault();
      return;
    }
  }
</script>

<form method="POST" action={url ?? "?/submit"}>
  <div class="fromBox">
    {#each options.filter((item) => !item.hidden) as item (item.key)}
      <div class={['item-label', 'leading-8', { 'required-label': item.required }]}>{item.label}:</div>

      {#if item.useFormElSnippet && formEl}
        {@render formEl(item)}
      {:else}
        <FormEl bind:value={value[item.key]} itemObj={item} bind:this={formElInstance[item.key]} {disabled} formData={value} />
      {/if}
    {/each}
  </div>

  <!-- 底部 -->
  {#if footerEl}
    {@render footerEl()}
  {:else}
    <div class="text-center mt-3">
      {#if !hideSubmitButton}
        <!-- 提交按钮 -->
        <button class="btn  btn-primary btn-sm" onclick={onclickHandle}>提交</button>
      {/if}
    </div>
  {/if}
</form>

<style>
	.required-label:before {
		content: '*';
		color: var(--color-error);
		margin-right: 4px;
	}
	.fromBox {
		@media (min-width: 1022px) {
			display: grid;
			gap: 0 12px;
			grid-template-columns: auto 1fr;

			.item-label {
				text-align: right;
			}
		}
	}
</style>
