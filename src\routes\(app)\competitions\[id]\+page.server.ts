/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2025-06-24
 * @LastEditors: <PERSON><PERSON>er
 * @LastEditTime: 2025-06-24
 * @Description: 
 * @FilePath: \training-camp-web\src\routes\(app)\competitions\[id]\+page.server.ts
 */
import type { Actions } from './$types';

export const actions = {
	submit: async (event) => {
    console.log('%c [ event ]-5', 'font-size:13px; background:#1ab1ae; color:#5ef5f2;', Object.keys(event));
    const data = await event.request.formData();
    console.log('%c [ data ]-15', 'font-size:13px; background:#24db8b; color:#68ffcf;', [...data.entries()]);
    console.log('%c [ data ]-15', 'font-size:13px; background:#24db8b; color:#68ffcf;', data.get('city'));
  },
} satisfies Actions;