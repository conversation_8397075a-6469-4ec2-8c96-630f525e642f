<script lang="ts">
	import Header from '$lib/components/layout/header.svelte';
	import SideMenu from '$lib/components/layout/sideMenu.svelte';
	import Footer from '$lib/components/layout/footer.svelte';

	// Svelte 5 的 $props() 用法，确保子内容可以被正确渲染
	const { children } = $props();

</script>

<!-- 顶部导航栏 -->
<Header />

<div class="overflow-hidden">
	<div class="drawer lg:drawer-open h-full">
		<!-- 用于触发侧边栏的显影 不能删除 -->
		<input id="my-drawer-2" type="checkbox" class="drawer-toggle" />

		<div class="drawer-content flex flex-col h-[calc(100vh-4rem)]">
			<!-- 路由渲染的内容 -->
			<main class="flex-1 w-full box-border p-4 overflow-y-auto">
        {@render children()}
      </main>

			<!-- 底部 -->
			<!-- <Footer /> -->
		</div>
		<!-- 侧边栏 -->
		<SideMenu />
	</div>
</div>

<style lang="postcss">
	/* 防止水平滚动条出现 */
	:global(body) {
		overflow-x: hidden;
	}

	/* 确保侧边栏在大屏幕下正确显示 */
	:global(.drawer-side) {
		@media (min-width: 1024px) {
			position: sticky;
			top: 4rem;
		}
	}
</style>
