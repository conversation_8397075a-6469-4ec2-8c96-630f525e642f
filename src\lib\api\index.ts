// src/lib/api.ts
import { PUBLIC_API_BASE_URL } from '$env/static/public';
// import { redirect } from '@sveltejs/kit'; // 这个导入只能在服务器端 load/action 中安全使用

// 为了在客户端也能重定向，我们需要 $app/navigation 模块
// 注意：这个导入只有在客户端运行时才有效。
// SvelteKit 会智能地处理它，在服务器端构建时会将其移除。
import { goto } from '$app/navigation';


/**
 * 封装通用 API 请求方法。
 * 该方法兼容服务器端和客户端，并处理 401 未授权重定向。
 *
 * @param path - API 的相对路径，例如 '/posts'。
 * @param options - 原生 fetch API 的请求选项。
 * @param customFetch - 从 SvelteKit load 函数（或 actions）传入的 fetch 函数。
 * 在服务器端或客户端路由时，必须传入这个 fetch 以便正确处理请求。
 * @param throwRedirect - 可选参数，指示是否在遇到 401 时抛出 SvelteKit 的 redirect 异常。
 * 在 load 或 action 中应设置为 true。在客户端组件中则不设置或设置为 false。
 * @returns Promise<T> - 解析为响应的 JSON 数据。
 */
export async function api<T>(
  path: string,
  options?: RequestInit,
  customFetch: typeof fetch = fetch, // 默认使用全局 fetch
  throwRedirect: boolean = false // 默认不抛出 redirect
): Promise<T> {
  const url = path.startsWith('http') ? path : `${PUBLIC_API_BASE_URL || ''}${path}`;

  const response = await customFetch(url, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...options,
  });


  const loginPath = '/login';
  if (response.status === 401) {
    if (throwRedirect) {
      // 只有在 load/action 中才能安全地抛出 redirect
      // 我们需要动态导入 redirect，因为 @sveltejs/kit 在客户端是不可用的
      const { redirect } = await import('@sveltejs/kit');
      throw redirect(302, loginPath); // 重定向到登录页
    } else {
      // 在客户端（非 load/action）则使用 goto 进行导航
      // 通常在客户端 401 意味着会话过期，需要用户重新登录
      console.warn('API 请求返回 401 Unauthorized。重定向到登录页...');
      goto(loginPath); // 客户端导航
      // 为了防止代码继续执行，可以抛出一个错误
      throw new Error('Unauthorized: Redirecting to login page.');
    }
  }

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ message: '未知错误' }));
    throw new Error(`API 请求失败: ${response.status} - ${errorData.message || response.statusText}`);
  }

  if (response.status === 204) {
    return null as T;
  }

  return await response.json() as T;
}

// 保持封装的 GET/POST/PUT/DELETE 方法
const get = async <T>(path: string, customFetch?: typeof fetch, throwRedirect: boolean = false): Promise<T> => {
  return api<T>(path, { method: 'GET' }, customFetch, throwRedirect);
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const post = async <T>(path: string, body: any, customFetch?: typeof fetch, throwRedirect: boolean = false): Promise<T> => {
  return api<T>(path, { method: 'POST', body: JSON.stringify(body) }, customFetch, throwRedirect);
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const put = async <T>(path: string, body: any, customFetch?: typeof fetch, throwRedirect: boolean = false): Promise<T> => {
  return api<T>(path, { method: 'PUT', body: JSON.stringify(body) }, customFetch, throwRedirect);
};

const del = async <T>(path: string, customFetch?: typeof fetch, throwRedirect: boolean = false): Promise<T> => {
  return api<T>(path, { method: 'DELETE' }, customFetch, throwRedirect);
};

export default {
  get,
  post,
  put,
  del
}