<script lang="ts">
	import Form from '$lib/components/form/form.svelte';
	import type FormType from '$lib/components/form/form.svelte';
	import type { FormElType } from '$lib/components/form';
	import { mapToObj } from 'remeda';
	import { CurrentIdentityMap, GenderMap, LearningStatusMap, WorkingStatusMap } from '$lib/dataModel/person';

	let processData = $state([
		{
			title: '注册/登录',
			status: 1 // 0 未完成，1已完成
		},
		{
			title: '报名信息',
			status: 0 // 0 未完成，1已完成
		}
	]);

	let pageType = $state<'login' | 'signUp'>('signUp');

	// ----------------登录/注册begin---------------------------
	const loginOptions: FormElType[] = [
		{
			key: 'phone',
			label: '手机号',
			required: true,
			type: 'phone'
		},
		{
			key: 'identifyCode',
			label: '验证码',
			required: true,
			width: 'calc(100% - 100px)'
		},
		{
			key: 'city',
			label: '城市',
			required: true,
			type: 'city'
		}
	];

	let loginFormData = $state(mapToObj(loginOptions, (item) => [item.key, null]));
	// ----------------登录/注册end---------------------------

	// ----------------报名信息begin---------------------------
	const signUpOptions: FormElType[] = [
		{
			key: 'xm',
			label: '姓名',
			required: true,
		},
    {
			key: 'xb',
			label: '性别',
			required: true,
      type: 'radio',
			options: Object.entries(GenderMap).map(([key, value]) => ({ value: Number(key), label: value })),
		},
    {
			key: 'sjh',
			label: '手机号', // TODO: 自动填充
			required: true,
      type: 'phone'
		},
    {
			key: 'zzyx',
			label: '电子邮箱',
			required: true,
      type: 'email'
		},
    {
			key: 'dqsf',
			label: '当前身份',
			required: true,
      type: 'radio',
			options: Object.entries(CurrentIdentityMap).map(([key, value]) => ({ value: Number(key), label: value })),
		},
    {
			key: 'xxzt',
			label: '学习状态',
			required: true,
      type: 'radio',
			options: Object.entries(LearningStatusMap).map(([key, value]) => ({ value: Number(key), label: value })),
		},
    {
			key: 'szgx',
			label: '所在高校',
			required: true,
		},
    {
			key: 'xxzy',
			label: '学习专业',
			required: true,
		},
    {
			key: 'szcs',
			label: '所在城市',
			required: true,
      type: 'city'
		},
    {
			key: 'gzzt',
			label: '工作状态',
			required: true,
      type: 'radio',
			options: Object.entries(WorkingStatusMap).map(([key, value]) => ({ value: Number(key), label: value })),
		},
    {
			key: 'gsmc',
			label: '公司名称',
			required: true,
		},
    {
			key: 'scjs',
			label: '擅长技术',
			required: true,
		},
    {
			key: 'GithubName',
			label: 'GithubName',
			required: true,
		},
    {
			key: 'GiteeName',
			label: 'GiteeName',
			required: true,
		},
    {
			key: 'AtomgitName',
			label: 'AtomgitName',
			required: true,
		},
	];

	let signUpFormData = $state(mapToObj(signUpOptions, (item) => [item.key, null]));
	// ----------------报名信息end---------------------------
</script>

<!-- 进度轴 -->
<ul class="timeline mt-10 ml-10">
	{#each processData as item, index (item.title)}
		<li>
			{#if index !== 0}
				<hr class={[{ 'bg-primary': item.status === 0 }]} />
			{/if}
			<div class="timeline-start">{item.title}</div>
			<div class="timeline-middle">
				<svg
					xmlns="http://www.w3.org/2000/svg"
					viewBox="0 0 20 20"
					fill="currentColor"
					class={[
						'h-5 w-5',
						{
							'text-primary': item.status === 1
						}
					]}
				>
					<path
						fill-rule="evenodd"
						d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z"
						clip-rule="evenodd"
					/>
				</svg>
			</div>
			{#if index !== processData.length - 1}
				<hr class={[{ 'bg-primary': item.status === 1 }]} />
			{/if}
		</li>
	{/each}
</ul>

<!-- 登录/注册 -->
{#if pageType === 'login'}
	<div class="mx-auto mt-10 max-w-100">
		<div class="text-2xl font-bold">注册/登录</div>

		<div class="relative mt-10">
			<Form options={loginOptions} bind:value={loginFormData} />
			<div class="identify-button">
				<button class="btn btn-primary btn-sm">发送验证码</button>
			</div>
		</div>
	</div>
{/if}

<!-- 报名信息 -->
{#if pageType === 'signUp'}
	<div class="mx-auto mt-10 max-w-140">
		<div class="text-2xl font-bold">报名信息</div>

		<div class="relative mt-10">
			<Form options={signUpOptions} bind:value={signUpFormData} />
		</div>
	</div>
{/if}

<style>
	.identify-button {
		position: absolute;
		right: 0px;
		top: 110px;
	}
	@media (min-width: 1022px) {
		.identify-button {
			position: absolute;
			right: 0px;
			top: 47px;
		}
	}
</style>
